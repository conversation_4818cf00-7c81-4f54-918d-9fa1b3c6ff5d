const { EmbedBuilder } = require('discord.js');

class Commands {
    constructor(database, aviatorGame) {
        this.db = database;
        this.aviator = aviatorGame;
    }

    // أمر فحص الرصيد
    async balance(message, args) {
        try {
            const userId = message.author.id;
            const username = message.author.username;

            // إنشاء المستخدم إذا لم يكن موجوداً
            await this.db.createUser(userId, username);
            const user = await this.db.getUser(userId);

            const embed = new EmbedBuilder()
                .setTitle('💰 رصيدك')
                .setColor('#00ff00')
                .setDescription(`**${user.balance}** كوينز`)
                .addFields(
                    { name: '🎮 الألعاب المُلعبة', value: user.games_played.toString(), inline: true },
                    { name: '🏆 إجمالي الأرباح', value: user.total_won.toString(), inline: true },
                    { name: '📉 إجمالي الخسائر', value: user.total_lost.toString(), inline: true }
                )
                .setThumbnail(message.author.displayAvatarURL())
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('خطأ في أمر الرصيد:', error);
            await message.reply('❌ حدث خطأ أثناء جلب رصيدك!');
        }
    }

    // أمر المكافأة اليومية
    async daily(message, args) {
        try {
            const userId = message.author.id;
            const username = message.author.username;

            await this.db.createUser(userId, username);
            const user = await this.db.getUser(userId);

            const today = new Date().toISOString().split('T')[0];
            const lastClaim = user.daily_claimed;

            if (lastClaim === today) {
                const embed = new EmbedBuilder()
                    .setTitle('⏰ مكافأة يومية')
                    .setColor('#ff9900')
                    .setDescription('لقد حصلت على مكافأتك اليومية بالفعل!\nعد غداً للحصول على مكافأة جديدة.')
                    .setTimestamp();

                await message.reply({ embeds: [embed] });
                return;
            }

            await this.db.claimDaily(userId);

            const embed = new EmbedBuilder()
                .setTitle('🎁 مكافأة يومية')
                .setColor('#00ff00')
                .setDescription('تهانينا! حصلت على **100 كوينز** كمكافأة يومية!')
                .addFields({ name: '💰 رصيدك الجديد', value: `${user.balance + 100} كوينز`, inline: true })
                .setThumbnail(message.author.displayAvatarURL())
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('خطأ في المكافأة اليومية:', error);
            await message.reply('❌ حدث خطأ أثناء الحصول على المكافأة اليومية!');
        }
    }

    // أمر التحويل
    async transfer(message, args) {
        try {
            if (args.length < 2) {
                await message.reply('❌ الاستخدام: `!تحويل @المستخدم المبلغ`');
                return;
            }

            const targetUser = message.mentions.users.first();
            const amount = parseInt(args[1]);

            if (!targetUser) {
                await message.reply('❌ يجب ذكر المستخدم المراد التحويل إليه!');
                return;
            }

            if (targetUser.id === message.author.id) {
                await message.reply('❌ لا يمكنك تحويل الأموال لنفسك!');
                return;
            }

            if (isNaN(amount) || amount <= 0) {
                await message.reply('❌ يجب أن يكون المبلغ رقماً موجباً!');
                return;
            }

            const senderId = message.author.id;
            const senderName = message.author.username;
            const receiverId = targetUser.id;
            const receiverName = targetUser.username;

            // إنشاء المستخدمين إذا لم يكونا موجودين
            await this.db.createUser(senderId, senderName);
            await this.db.createUser(receiverId, receiverName);

            const sender = await this.db.getUser(senderId);

            if (sender.balance < amount) {
                await message.reply('❌ رصيدك غير كافي لهذا التحويل!');
                return;
            }

            // تنفيذ التحويل
            await this.db.updateBalance(senderId, -amount);
            await this.db.updateBalance(receiverId, amount);

            const embed = new EmbedBuilder()
                .setTitle('💸 تحويل ناجح')
                .setColor('#00ff00')
                .setDescription(`تم تحويل **${amount} كوينز** إلى ${targetUser}`)
                .addFields(
                    { name: '💰 رصيدك الجديد', value: `${sender.balance - amount} كوينز`, inline: true }
                )
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('خطأ في التحويل:', error);
            await message.reply('❌ حدث خطأ أثناء التحويل!');
        }
    }

    // أمر قائمة المتصدرين
    async leaderboard(message, args) {
        try {
            const leaders = await this.db.getLeaderboard(10);

            if (leaders.length === 0) {
                await message.reply('❌ لا توجد بيانات للمتصدرين بعد!');
                return;
            }

            const embed = new EmbedBuilder()
                .setTitle('🏆 قائمة المتصدرين')
                .setColor('#ffd700')
                .setTimestamp();

            let description = '';
            leaders.forEach((user, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                description += `${medal} **${user.username}** - ${user.balance} كوينز\n`;
            });

            embed.setDescription(description);
            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('خطأ في قائمة المتصدرين:', error);
            await message.reply('❌ حدث خطأ أثناء جلب قائمة المتصدرين!');
        }
    }

    // أمر المساعدة
    async help(message, args) {
        const embed = new EmbedBuilder()
            .setTitle('📋 قائمة الأوامر')
            .setColor('#0099ff')
            .setDescription('جميع أوامر البوت المتاحة:')
            .addFields(
                { name: '💰 !رصيد', value: 'عرض رصيدك الحالي', inline: true },
                { name: '🎁 !يومي', value: 'الحصول على المكافأة اليومية', inline: true },
                { name: '💸 !تحويل @مستخدم مبلغ', value: 'تحويل الأموال لمستخدم آخر', inline: true },
                { name: '🏆 !متصدرين', value: 'عرض قائمة المتصدرين', inline: true },
                { name: '🛩️ !طائرة مبلغ', value: 'لعب لعبة الطائرة', inline: true },
                { name: '📊 !احصائيات', value: 'عرض إحصائيات الألعاب', inline: true }
            )
            .setFooter({ text: 'بوت ترفيهي - عملة وهمية فقط!' })
            .setTimestamp();

        await message.reply({ embeds: [embed] });
    }

    // أمر إحصائيات الألعاب
    async stats(message, args) {
        try {
            const aviatorStats = this.aviator.getStats();
            
            const embed = new EmbedBuilder()
                .setTitle('📊 إحصائيات الألعاب')
                .setColor('#9932cc')
                .addFields(
                    { name: '🛩️ ألعاب الطائرة النشطة', value: aviatorStats.activeGames.toString(), inline: true },
                    { name: '📈 آخر النتائج', value: aviatorStats.recentCrashes.length > 0 ? 
                        aviatorStats.recentCrashes.map(g => `${g.multiplier.toFixed(2)}x`).join(' • ') : 
                        'لا توجد ألعاب بعد', inline: false }
                )
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('خطأ في الإحصائيات:', error);
            await message.reply('❌ حدث خطأ أثناء جلب الإحصائيات!');
        }
    }
}

module.exports = Commands;
