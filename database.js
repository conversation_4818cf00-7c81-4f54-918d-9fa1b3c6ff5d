const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
    constructor() {
        this.db = new sqlite3.Database(path.join(__dirname, 'bot.db'));
        this.init();
    }

    init() {
        // إنشاء جدول المستخدمين والعملة
        this.db.run(`
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT,
                balance INTEGER DEFAULT 1000,
                daily_claimed DATE,
                total_won INTEGER DEFAULT 0,
                total_lost INTEGER DEFAULT 0,
                games_played INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // إنشاء جدول تاريخ الألعاب
        this.db.run(`
            CREATE TABLE IF NOT EXISTS game_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                game_type TEXT,
                bet_amount INTEGER,
                multiplier REAL,
                win_amount INTEGER,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        `);
    }

    // الحصول على بيانات المستخدم
    getUser(userId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE user_id = ?',
                [userId],
                (err, row) => {
                    if (err) reject(err);
                    resolve(row);
                }
            );
        });
    }

    // إنشاء مستخدم جديد
    createUser(userId, username) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'INSERT OR IGNORE INTO users (user_id, username) VALUES (?, ?)',
                [userId, username],
                function(err) {
                    if (err) reject(err);
                    resolve(this.lastID);
                }
            );
        });
    }

    // تحديث رصيد المستخدم
    updateBalance(userId, amount) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE users SET balance = balance + ? WHERE user_id = ?',
                [amount, userId],
                function(err) {
                    if (err) reject(err);
                    resolve(this.changes);
                }
            );
        });
    }

    // تسجيل المكافأة اليومية
    claimDaily(userId) {
        const today = new Date().toISOString().split('T')[0];
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE users SET balance = balance + 100, daily_claimed = ? WHERE user_id = ?',
                [today, userId],
                function(err) {
                    if (err) reject(err);
                    resolve(this.changes);
                }
            );
        });
    }

    // تسجيل نتيجة اللعبة
    recordGame(userId, gameType, betAmount, multiplier, winAmount) {
        return new Promise((resolve, reject) => {
            // تسجيل في تاريخ الألعاب
            this.db.run(
                'INSERT INTO game_history (user_id, game_type, bet_amount, multiplier, win_amount) VALUES (?, ?, ?, ?, ?)',
                [userId, gameType, betAmount, multiplier, winAmount],
                (err) => {
                    if (err) reject(err);
                    
                    // تحديث إحصائيات المستخدم
                    const isWin = winAmount > betAmount;
                    const profit = winAmount - betAmount;
                    
                    this.db.run(
                        `UPDATE users SET 
                         balance = balance + ?,
                         total_won = total_won + ?,
                         total_lost = total_lost + ?,
                         games_played = games_played + 1
                         WHERE user_id = ?`,
                        [profit, isWin ? winAmount : 0, isWin ? 0 : betAmount, userId],
                        function(err) {
                            if (err) reject(err);
                            resolve(this.changes);
                        }
                    );
                }
            );
        });
    }

    // الحصول على أفضل اللاعبين
    getLeaderboard(limit = 10) {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT username, balance, total_won, games_played FROM users ORDER BY balance DESC LIMIT ?',
                [limit],
                (err, rows) => {
                    if (err) reject(err);
                    resolve(rows);
                }
            );
        });
    }

    close() {
        this.db.close();
    }
}

module.exports = Database;
