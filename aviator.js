const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, But<PERSON><PERSON>uilder, ButtonStyle } = require('discord.js');

class AviatorGame {
    constructor() {
        this.activeGames = new Map(); // userId -> gameData
        this.gameHistory = [];
    }

    // بدء لعبة جديدة
    startGame(userId, betAmount) {
        if (this.activeGames.has(userId)) {
            return { error: 'لديك لعبة نشطة بالفعل!' };
        }

        const gameData = {
            userId,
            betAmount,
            startTime: Date.now(),
            multiplier: 1.00,
            crashed: false,
            cashedOut: false,
            cashOutMultiplier: null
        };

        this.activeGames.set(userId, gameData);
        return { success: true, gameData };
    }

    // سحب الرهان
    cashOut(userId) {
        const game = this.activeGames.get(userId);
        if (!game) {
            return { error: 'لا توجد لعبة نشطة!' };
        }

        if (game.crashed) {
            return { error: 'الطائرة تحطمت بالفعل!' };
        }

        if (game.cashedOut) {
            return { error: 'تم السحب بالفعل!' };
        }

        game.cashedOut = true;
        game.cashOutMultiplier = game.multiplier;
        const winAmount = Math.floor(game.betAmount * game.multiplier);

        this.activeGames.delete(userId);
        
        return {
            success: true,
            multiplier: game.multiplier,
            winAmount,
            profit: winAmount - game.betAmount
        };
    }

    // تحديث اللعبة (يتم استدعاؤها كل ثانية)
    updateGame(userId) {
        const game = this.activeGames.get(userId);
        if (!game || game.crashed || game.cashedOut) {
            return null;
        }

        const elapsed = (Date.now() - game.startTime) / 1000;
        
        // حساب المضاعف بناءً على الوقت
        game.multiplier = 1 + (elapsed * 0.1) + (Math.random() * 0.05);
        
        // احتمال تحطم الطائرة (يزيد مع الوقت)
        const crashChance = Math.min(0.02 + (elapsed * 0.001), 0.15);
        
        if (Math.random() < crashChance) {
            game.crashed = true;
            game.crashMultiplier = game.multiplier;
            this.activeGames.delete(userId);
            
            // إضافة إلى التاريخ
            this.gameHistory.unshift({
                multiplier: game.multiplier,
                timestamp: Date.now()
            });
            
            // الاحتفاظ بآخر 10 ألعاب فقط
            if (this.gameHistory.length > 10) {
                this.gameHistory.pop();
            }
            
            return {
                crashed: true,
                multiplier: game.multiplier,
                lost: true
            };
        }

        return {
            multiplier: game.multiplier,
            crashed: false
        };
    }

    // إنشاء رسالة اللعبة
    createGameEmbed(gameData, crashed = false) {
        const embed = new EmbedBuilder()
            .setTitle('🛩️ لعبة الطائرة')
            .setColor(crashed ? '#ff0000' : '#00ff00');

        if (crashed) {
            embed.setDescription(`💥 **تحطمت الطائرة!**\n\n🔥 المضاعف النهائي: **${gameData.multiplier.toFixed(2)}x**\n💰 الرهان: **${gameData.betAmount}** كوينز\n❌ خسارة: **${gameData.betAmount}** كوينز`);
        } else {
            const currentWin = Math.floor(gameData.betAmount * gameData.multiplier);
            embed.setDescription(`✈️ **الطائرة تحلق!**\n\n📈 المضاعف الحالي: **${gameData.multiplier.toFixed(2)}x**\n💰 الرهان: **${gameData.betAmount}** كوينز\n💵 الربح المحتمل: **${currentWin}** كوينز\n\n⚡ اضغط "سحب" قبل أن تتحطم الطائرة!`);
        }

        // إضافة تاريخ الألعاب السابقة
        if (this.gameHistory.length > 0) {
            const historyText = this.gameHistory
                .slice(0, 5)
                .map(game => `${game.multiplier.toFixed(2)}x`)
                .join(' • ');
            embed.addFields({
                name: '📊 آخر النتائج',
                value: historyText,
                inline: false
            });
        }

        return embed;
    }

    // إنشاء أزرار اللعبة
    createGameButtons(crashed = false) {
        const row = new ActionRowBuilder();
        
        if (!crashed) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('aviator_cashout')
                    .setLabel('💰 سحب')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('aviator_cancel')
                    .setLabel('❌ إلغاء')
                    .setStyle(ButtonStyle.Danger)
            );
        } else {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('aviator_new_game')
                    .setLabel('🎮 لعبة جديدة')
                    .setStyle(ButtonStyle.Primary)
            );
        }

        return row;
    }

    // الحصول على لعبة نشطة
    getActiveGame(userId) {
        return this.activeGames.get(userId);
    }

    // إلغاء اللعبة
    cancelGame(userId) {
        if (this.activeGames.has(userId)) {
            this.activeGames.delete(userId);
            return true;
        }
        return false;
    }

    // الحصول على إحصائيات
    getStats() {
        return {
            activeGames: this.activeGames.size,
            recentCrashes: this.gameHistory.slice(0, 5)
        };
    }
}

module.exports = AviatorGame;
