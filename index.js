require('dotenv').config();
const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');
const Database = require('./database');
const AviatorGame = require('./aviator');
const Commands = require('./commands');

// إنشاء العميل
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMessageReactions
    ]
});

// إنشاء قاعدة البيانات ولعبة الطائرة والأوامر
const database = new Database();
const aviatorGame = new AviatorGame();
const commands = new Commands(database, aviatorGame);

// خريطة الألعاب النشطة لتحديثها
const gameUpdates = new Map();

// عند تشغيل البوت
client.once('ready', () => {
    console.log(`✅ البوت جاهز! تم تسجيل الدخول باسم ${client.user.tag}`);
    client.user.setActivity('🛩️ لعبة الطائرة | !مساعدة', { type: 'PLAYING' });
    
    // بدء تحديث الألعاب كل ثانية
    setInterval(updateActiveGames, 1000);
});

// معالجة الرسائل
client.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    
    const args = message.content.slice(1).trim().split(/ +/);
    const command = args.shift().toLowerCase();

    try {
        switch (command) {
            case 'رصيد':
            case 'balance':
                await commands.balance(message, args);
                break;
                
            case 'يومي':
            case 'daily':
                await commands.daily(message, args);
                break;
                
            case 'تحويل':
            case 'transfer':
                await commands.transfer(message, args);
                break;
                
            case 'متصدرين':
            case 'leaderboard':
                await commands.leaderboard(message, args);
                break;
                
            case 'مساعدة':
            case 'help':
                await commands.help(message, args);
                break;
                
            case 'احصائيات':
            case 'stats':
                await commands.stats(message, args);
                break;
                
            case 'طائرة':
            case 'aviator':
                await handleAviatorCommand(message, args);
                break;
        }
    } catch (error) {
        console.error('خطأ في معالجة الأمر:', error);
        await message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
    }
});

// معالجة أمر لعبة الطائرة
async function handleAviatorCommand(message, args) {
    if (args.length === 0) {
        await message.reply('❌ الاستخدام: `!طائرة المبلغ`\nمثال: `!طائرة 100`');
        return;
    }

    const betAmount = parseInt(args[0]);
    if (isNaN(betAmount) || betAmount <= 0) {
        await message.reply('❌ يجب أن يكون المبلغ رقماً موجباً!');
        return;
    }

    const userId = message.author.id;
    const username = message.author.username;

    try {
        // التحقق من الرصيد
        await database.createUser(userId, username);
        const user = await database.getUser(userId);

        if (user.balance < betAmount) {
            await message.reply('❌ رصيدك غير كافي لهذا الرهان!');
            return;
        }

        // بدء اللعبة
        const gameResult = aviatorGame.startGame(userId, betAmount);
        if (gameResult.error) {
            await message.reply(`❌ ${gameResult.error}`);
            return;
        }

        // خصم المبلغ من الرصيد
        await database.updateBalance(userId, -betAmount);

        // إرسال رسالة اللعبة
        const embed = aviatorGame.createGameEmbed(gameResult.gameData);
        const buttons = aviatorGame.createGameButtons();
        
        const gameMessage = await message.reply({
            embeds: [embed],
            components: [buttons]
        });

        // حفظ معرف الرسالة للتحديث
        gameUpdates.set(userId, {
            message: gameMessage,
            lastUpdate: Date.now()
        });

    } catch (error) {
        console.error('خطأ في لعبة الطائرة:', error);
        await message.reply('❌ حدث خطأ أثناء بدء اللعبة!');
    }
}

// تحديث الألعاب النشطة
async function updateActiveGames() {
    for (const [userId, gameUpdate] of gameUpdates.entries()) {
        try {
            const gameData = aviatorGame.updateGame(userId);
            if (!gameData) {
                gameUpdates.delete(userId);
                continue;
            }

            // تحديث الرسالة كل ثانية
            if (Date.now() - gameUpdate.lastUpdate >= 1000) {
                const embed = aviatorGame.createGameEmbed(
                    aviatorGame.getActiveGame(userId) || gameData, 
                    gameData.crashed
                );
                const buttons = aviatorGame.createGameButtons(gameData.crashed);

                await gameUpdate.message.edit({
                    embeds: [embed],
                    components: gameData.crashed ? [buttons] : [buttons]
                });

                gameUpdate.lastUpdate = Date.now();

                // إذا تحطمت الطائرة، سجل النتيجة وأزل التحديث
                if (gameData.crashed) {
                    const activeGame = aviatorGame.getActiveGame(userId);
                    if (activeGame) {
                        await database.recordGame(
                            userId, 
                            'aviator', 
                            activeGame.betAmount, 
                            gameData.multiplier, 
                            0
                        );
                    }
                    gameUpdates.delete(userId);
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث اللعبة:', error);
            gameUpdates.delete(userId);
        }
    }
}

// معالجة الأزرار
client.on('interactionCreate', async (interaction) => {
    if (!interaction.isButton()) return;

    const userId = interaction.user.id;

    try {
        if (interaction.customId === 'aviator_cashout') {
            const result = aviatorGame.cashOut(userId);
            
            if (result.error) {
                await interaction.reply({ content: `❌ ${result.error}`, ephemeral: true });
                return;
            }

            // تسجيل الربح
            await database.recordGame(userId, 'aviator', result.winAmount - result.profit, result.multiplier, result.winAmount);

            const embed = new EmbedBuilder()
                .setTitle('💰 سحب ناجح!')
                .setColor('#00ff00')
                .setDescription(`🎉 **تهانينا!**\n\n📈 المضاعف: **${result.multiplier.toFixed(2)}x**\n💰 الربح: **${result.winAmount}** كوينز\n📊 الأرباح الصافية: **+${result.profit}** كوينز`)
                .setTimestamp();

            await interaction.update({
                embeds: [embed],
                components: []
            });

            gameUpdates.delete(userId);

        } else if (interaction.customId === 'aviator_cancel') {
            aviatorGame.cancelGame(userId);
            
            // إرجاع المبلغ
            const activeGame = aviatorGame.getActiveGame(userId);
            if (activeGame) {
                await database.updateBalance(userId, activeGame.betAmount);
            }

            await interaction.update({
                content: '❌ تم إلغاء اللعبة وإرجاع المبلغ.',
                embeds: [],
                components: []
            });

            gameUpdates.delete(userId);

        } else if (interaction.customId === 'aviator_new_game') {
            await interaction.reply({
                content: '🎮 لبدء لعبة جديدة، استخدم الأمر: `!طائرة المبلغ`',
                ephemeral: true
            });
        }
    } catch (error) {
        console.error('خطأ في معالجة الزر:', error);
        await interaction.reply({ content: '❌ حدث خطأ!', ephemeral: true });
    }
});

// تسجيل الدخول
console.log('🔍 التحقق من التوكن...');
console.log('Token exists:', !!process.env.DISCORD_TOKEN);
console.log('Token length:', process.env.DISCORD_TOKEN ? process.env.DISCORD_TOKEN.length : 0);

if (!process.env.DISCORD_TOKEN) {
    console.error('❌ لم يتم العثور على DISCORD_TOKEN في ملف .env');
    process.exit(1);
}

client.login(process.env.DISCORD_TOKEN).catch(error => {
    console.error('❌ خطأ في تسجيل الدخول:', error.message);
    process.exit(1);
});
