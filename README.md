# 🛩️ بوت Discord - لعبة الطائرة الترفيهية

بوت Discord ترفيهي يحتوي على لعبة الطائرة مع نظام عملة افتراضية للمتعة فقط!

## ✨ الميزات

### 💰 نظام العملة الافتراضية
- رصيد ابتدائي: 1000 كوينز
- مكافأة يومية: 100 كوينز
- تحويل الأموال بين المستخدمين
- قائمة المتصدرين

### 🛩️ لعبة الطائرة
- لعبة مثيرة مع مضاعفات متغيرة
- إمكانية السحب في أي وقت
- تاريخ النتائج السابقة
- إحصائيات مفصلة

### 📊 الأوامر المتاحة
- `!رصيد` - عرض رصيدك الحالي
- `!يومي` - الحصول على المكافأة اليومية
- `!تحويل @مستخدم مبلغ` - تحويل الأموال
- `!متصدرين` - قائمة أغنى اللاعبين
- `!طائرة مبلغ` - لعب لعبة الطائرة
- `!احصائيات` - إحصائيات الألعاب
- `!مساعدة` - قائمة جميع الأوامر

## 🚀 طريقة التشغيل

### 1. إنشاء بوت Discord
1. اذهب إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. اضغط "New Application"
3. اختر اسماً للتطبيق
4. اذهب إلى تبويب "Bot"
5. اضغط "Add Bot"
6. انسخ الـ Token

### 2. إعداد الصلاحيات
في تبويب "Bot":
- ✅ MESSAGE CONTENT INTENT
- ✅ SERVER MEMBERS INTENT

في تبويب "OAuth2" > "URL Generator":
- اختر "bot"
- اختر الصلاحيات:
  - Send Messages
  - Use Slash Commands
  - Read Message History
  - Add Reactions

### 3. تشغيل البوت
1. افتح ملف `.env`
2. استبدل `YOUR_BOT_TOKEN_HERE` بـ token البوت الحقيقي
3. شغل الأمر:
```bash
npm start
```

## 🎮 طريقة اللعب

### لعبة الطائرة
1. استخدم `!طائرة 100` (مثلاً) لبدء اللعبة
2. ستبدأ الطائرة بالطيران والمضاعف بالارتفاع
3. اضغط "💰 سحب" قبل أن تتحطم الطائرة
4. إذا تحطمت قبل السحب، تخسر الرهان
5. إذا سحبت في الوقت المناسب، تربح المبلغ × المضاعف

### نصائح
- ابدأ برهانات صغيرة لتتعلم اللعبة
- لا تطمع كثيراً - السحب المبكر أفضل من الخسارة
- استخدم المكافأة اليومية لزيادة رصيدك
- راقب تاريخ النتائج السابقة

## ⚠️ تنبيه مهم
هذا البوت للترفيه فقط! العملة المستخدمة وهمية وليس لها أي قيمة حقيقية.

## 🛠️ التطوير
البوت مكتوب بـ Node.js ويستخدم:
- Discord.js v14
- SQLite3 لقاعدة البيانات
- dotenv لإدارة المتغيرات

## 📝 الملفات
- `index.js` - الملف الرئيسي
- `database.js` - إدارة قاعدة البيانات
- `aviator.js` - منطق لعبة الطائرة
- `commands.js` - أوامر البوت
- `package.json` - إعدادات المشروع
- `.env` - متغيرات البيئة (Token)

## 🎯 استمتع باللعب!
البوت جاهز للاستخدام. ادع أصدقاءك وتنافسوا في لعبة الطائرة الممتعة!
